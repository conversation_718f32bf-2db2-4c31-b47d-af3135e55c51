<?php

namespace App\Models;

use CodeIgniter\Model;

class CmsAdminModel extends Model
{
    protected $table = 'cms_admins';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'username',
        'email',
        'password',
        'full_name',
        'role',
        'status',
        'last_login',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'username' => 'required|min_length[3]|max_length[50]|is_unique[cms_admins.username,id,{id}]',
        'email' => 'required|valid_email|is_unique[cms_admins.email,id,{id}]',
        'password' => 'required|min_length[6]',
        'full_name' => 'required|min_length[2]|max_length[100]',
        'role' => 'required|in_list[super_admin,admin,editor]',
        'status' => 'required|in_list[active,inactive]'
    ];

    protected $validationMessages = [
        'username' => [
            'required' => 'Username is required',
            'min_length' => 'Username must be at least 3 characters long',
            'max_length' => 'Username cannot exceed 50 characters',
            'is_unique' => 'Username already exists'
        ],
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please enter a valid email address',
            'is_unique' => 'Email already exists'
        ],
        'password' => [
            'required' => 'Password is required',
            'min_length' => 'Password must be at least 6 characters long'
        ],
        'full_name' => [
            'required' => 'Full name is required',
            'min_length' => 'Full name must be at least 2 characters long',
            'max_length' => 'Full name cannot exceed 100 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    /**
     * Hash password before saving
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }
        return $data;
    }

    /**
     * Verify admin credentials
     */
    public function verifyCredentials($username, $password)
    {
        $admin = $this->where('username', $username)
                      ->where('status', 'active')
                      ->first();

        if ($admin && password_verify($password, $admin['password'])) {
            // Update last login
            $this->update($admin['id'], ['last_login' => date('Y-m-d H:i:s')]);
            return $admin;
        }

        return false;
    }

    /**
     * Get active admins
     */
    public function getActiveAdmins()
    {
        return $this->where('status', 'active')->orderBy('full_name', 'ASC')->findAll();
    }

    /**
     * Check if user has permission
     */
    public function hasPermission($adminId, $permission)
    {
        $admin = $this->find($adminId);
        if (!$admin) return false;

        $rolePermissions = [
            'super_admin' => ['all'],
            'admin' => ['pages', 'menus', 'content', 'users'],
            'editor' => ['pages', 'content']
        ];

        return in_array('all', $rolePermissions[$admin['role']] ?? []) || 
               in_array($permission, $rolePermissions[$admin['role']] ?? []);
    }
}
