<?php

namespace App\Services;

use App\Models\CmsPageModel;
use App\Models\DynamicRouteModel;
use CodeIgniter\Config\Services;

class DynamicRouteService
{
    protected $pageModel;
    protected $routeModel;
    protected $cache;

    public function __construct()
    {
        $this->pageModel = new CmsPageModel();
        $this->routeModel = new DynamicRouteModel();
        $this->cache = Services::cache();
    }

    /**
     * Generate and register routes for a page
     */
    public function generatePageRoute($pageId, $slug, $parentId = null)
    {
        $page = $this->pageModel->find($pageId);
        if (!$page) {
            return false;
        }

        // Generate full slug for hierarchical URLs
        $fullSlug = $this->generateFullSlug($slug, $parentId);
        
        // Update page with full slug
        $this->pageModel->update($pageId, ['full_slug' => $fullSlug]);

        // Check for route conflicts
        if ($this->hasRouteConflict($fullSlug, $pageId)) {
            return false;
        }

        // Create or update dynamic route
        $routeData = [
            'route_pattern' => $fullSlug,
            'controller_method' => 'CmsPage::viewDynamic/' . $pageId,
            'page_id' => $pageId,
            'route_type' => $parentId ? 'hierarchical' : 'page',
            'status' => 'active'
        ];

        // Check if route already exists
        $existingRoute = $this->routeModel->where('page_id', $pageId)->first();
        if ($existingRoute) {
            $this->routeModel->update($existingRoute['id'], $routeData);
        } else {
            $this->routeModel->insert($routeData);
        }

        // Clear route cache
        $this->clearRouteCache();

        return $fullSlug;
    }

    /**
     * Generate full slug including parent hierarchy
     */
    public function generateFullSlug($slug, $parentId = null)
    {
        if (!$parentId) {
            return $slug;
        }

        $parentPage = $this->pageModel->find($parentId);
        if (!$parentPage) {
            return $slug;
        }

        $parentSlug = $parentPage['full_slug'] ?: $parentPage['slug'];
        return $parentSlug . '/' . $slug;
    }

    /**
     * Check for route conflicts
     */
    public function hasRouteConflict($slug, $excludePageId = null)
    {
        // Check against existing pages
        $query = $this->pageModel->where('full_slug', $slug);
        if ($excludePageId) {
            $query->where('id !=', $excludePageId);
        }
        
        if ($query->first()) {
            return true;
        }

        // Check against static routes
        $staticRoutes = $this->getStaticRoutes();
        return in_array($slug, $staticRoutes);
    }

    /**
     * Get list of static routes to avoid conflicts
     */
    public function getStaticRoutes()
    {
        return [
            'admin',
            'api',
            'cms',
            'login',
            'logout',
            'register',
            'dashboard',
            'profile',
            'settings'
        ];
    }

    /**
     * Remove route for a page
     */
    public function removePageRoute($pageId)
    {
        $this->routeModel->where('page_id', $pageId)->delete();
        $this->clearRouteCache();
    }

    /**
     * Get all active dynamic routes
     */
    public function getActiveRoutes()
    {
        $cacheKey = 'dynamic_routes_active';
        $routes = $this->cache->get($cacheKey);

        if ($routes === null) {
            $routes = $this->routeModel->where('status', 'active')
                                      ->orderBy('route_pattern', 'ASC')
                                      ->findAll();
            $this->cache->save($cacheKey, $routes, 3600); // Cache for 1 hour
        }

        return $routes;
    }

    /**
     * Register dynamic routes with CodeIgniter router
     */
    public function registerDynamicRoutes(&$routes)
    {
        $dynamicRoutes = $this->getActiveRoutes();
        
        foreach ($dynamicRoutes as $route) {
            $pattern = $route['route_pattern'];
            $controller = $route['controller_method'];
            
            // Register the route
            $routes->get($pattern, $controller);
        }
    }

    /**
     * Clear route cache
     */
    public function clearRouteCache()
    {
        $this->cache->delete('dynamic_routes_active');
    }

    /**
     * Validate slug format
     */
    public function validateSlug($slug)
    {
        // Check if slug contains only allowed characters
        if (!preg_match('/^[a-z0-9\-\/]+$/', $slug)) {
            return false;
        }

        // Check for reserved words
        $reserved = ['admin', 'api', 'cms', 'login', 'logout'];
        $slugParts = explode('/', $slug);
        
        foreach ($slugParts as $part) {
            if (in_array($part, $reserved)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Generate unique slug
     */
    public function generateUniqueSlug($title, $parentId = null, $excludePageId = null)
    {
        $baseSlug = url_title($title, '-', true);
        $slug = $baseSlug;
        $counter = 1;

        while ($this->hasRouteConflict($this->generateFullSlug($slug, $parentId), $excludePageId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get page hierarchy for breadcrumbs
     */
    public function getPageHierarchy($pageId)
    {
        $hierarchy = [];
        $page = $this->pageModel->find($pageId);
        
        while ($page) {
            array_unshift($hierarchy, $page);
            $page = $page['parent_id'] ? $this->pageModel->find($page['parent_id']) : null;
        }

        return $hierarchy;
    }

    /**
     * Update child page routes when parent changes
     */
    public function updateChildRoutes($parentId)
    {
        $childPages = $this->pageModel->where('parent_id', $parentId)->findAll();
        
        foreach ($childPages as $child) {
            $this->generatePageRoute($child['id'], $child['slug'], $parentId);
            
            // Recursively update grandchildren
            $this->updateChildRoutes($child['id']);
        }
    }
}
