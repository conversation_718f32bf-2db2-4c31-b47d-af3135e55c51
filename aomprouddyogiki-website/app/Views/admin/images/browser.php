<?= $this->extend('admin/layouts/admin') ?>

<?= $this->section('head') ?>
<style>
    .image-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
        padding: 1rem 0;
    }
    
    .image-item {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
        cursor: pointer;
        background: white;
    }
    
    .image-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-color: #667eea;
    }
    
    .image-item.selected {
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
    }
    
    .image-preview {
        width: 100%;
        height: 150px;
        object-fit: cover;
        background: #f8f9fa;
    }
    
    .image-info {
        padding: 0.75rem;
    }
    
    .image-filename {
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.25rem;
        word-break: break-all;
    }
    
    .image-meta {
        font-size: 0.75rem;
        color: #6c757d;
    }
    
    .upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }
    
    .upload-area.dragover {
        border-color: #667eea;
        background-color: rgba(102, 126, 234, 0.05);
    }
    
    .upload-progress {
        display: none;
        margin-top: 1rem;
    }
    
    .modal-body .image-grid {
        max-height: 400px;
        overflow-y: auto;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-images me-2"></i>
        Image Browser
    </h2>
    <div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
            <i class="fas fa-upload me-2"></i>
            Upload Images
        </button>
        <button type="button" class="btn btn-outline-danger" id="deleteSelected" style="display: none;">
            <i class="fas fa-trash me-2"></i>
            Delete Selected
        </button>
    </div>
</div>

<!-- Upload Area -->
<div class="card mb-4">
    <div class="card-body">
        <div class="upload-area" id="uploadArea">
            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
            <h5>Drag & Drop Images Here</h5>
            <p class="text-muted">Or click to select files</p>
            <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
            <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('fileInput').click()">
                Choose Files
            </button>
        </div>
        <div class="upload-progress">
            <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
            <small class="text-muted mt-2 d-block">Uploading images...</small>
        </div>
    </div>
</div>

<!-- Images Grid -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Uploaded Images</h5>
    </div>
    <div class="card-body">
        <div id="imageGrid" class="image-grid">
            <!-- Images will be loaded here -->
        </div>
        <div id="noImages" class="text-center py-5" style="display: none;">
            <i class="fas fa-images fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No images uploaded yet</h5>
            <p class="text-muted">Upload your first image to get started</p>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Images</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="upload-area" id="modalUploadArea">
                    <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                    <p>Drag & drop images or click to select</p>
                    <input type="file" id="modalFileInput" multiple accept="image/*" style="display: none;">
                    <button type="button" class="btn btn-primary" onclick="document.getElementById('modalFileInput').click()">
                        Select Images
                    </button>
                </div>
                <div id="uploadQueue" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="uploadAllBtn" style="display: none;">Upload All</button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let selectedImages = new Set();
    
    // Load images on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadImages();
        setupUploadHandlers();
    });
    
    // Load images from server
    function loadImages() {
        fetch('<?= base_url('admin/images/browse') ?>')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayImages(data.images);
                } else {
                    showAlert('Error loading images: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Error loading images', 'danger');
            });
    }
    
    // Display images in grid
    function displayImages(images) {
        const grid = document.getElementById('imageGrid');
        const noImages = document.getElementById('noImages');
        
        if (images.length === 0) {
            grid.style.display = 'none';
            noImages.style.display = 'block';
            return;
        }
        
        grid.style.display = 'grid';
        noImages.style.display = 'none';
        
        grid.innerHTML = images.map(image => `
            <div class="image-item" data-filename="${image.filename}" onclick="toggleImageSelection('${image.filename}')">
                <img src="${image.thumbnail || image.url}" alt="${image.filename}" class="image-preview" loading="lazy">
                <div class="image-info">
                    <div class="image-filename">${image.filename}</div>
                    <div class="image-meta">
                        ${image.size} • ${new Date(image.modified).toLocaleDateString()}
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    // Toggle image selection
    function toggleImageSelection(filename) {
        const imageItem = document.querySelector(`[data-filename="${filename}"]`);
        
        if (selectedImages.has(filename)) {
            selectedImages.delete(filename);
            imageItem.classList.remove('selected');
        } else {
            selectedImages.add(filename);
            imageItem.classList.add('selected');
        }
        
        updateDeleteButton();
    }
    
    // Update delete button visibility
    function updateDeleteButton() {
        const deleteBtn = document.getElementById('deleteSelected');
        if (selectedImages.size > 0) {
            deleteBtn.style.display = 'inline-block';
            deleteBtn.textContent = `Delete Selected (${selectedImages.size})`;
        } else {
            deleteBtn.style.display = 'none';
        }
    }
    
    // Setup upload handlers
    function setupUploadHandlers() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });
        
        // File input
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });
        
        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
    }
    
    // Handle file uploads
    function handleFiles(files) {
        Array.from(files).forEach(file => {
            if (file.type.startsWith('image/')) {
                uploadFile(file);
            }
        });
    }
    
    // Upload single file
    function uploadFile(file) {
        const formData = new FormData();
        formData.append('image', file);
        
        fetch('<?= base_url('admin/images/upload') ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Image uploaded successfully: ' + data.filename, 'success');
                loadImages(); // Reload images
            } else {
                showAlert('Upload failed: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Upload error', 'danger');
        });
    }
    
    // Delete selected images
    document.getElementById('deleteSelected').addEventListener('click', function() {
        if (selectedImages.size === 0) return;
        
        if (confirm(`Are you sure you want to delete ${selectedImages.size} image(s)?`)) {
            selectedImages.forEach(filename => {
                deleteImage(filename);
            });
        }
    });
    
    // Delete single image
    function deleteImage(filename) {
        const formData = new FormData();
        formData.append('filename', filename);
        
        fetch('<?= base_url('admin/images/delete') ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                selectedImages.delete(filename);
                loadImages(); // Reload images
                updateDeleteButton();
            } else {
                showAlert('Delete failed: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Delete error', 'danger');
        });
    }
    
    // Show alert
    function showAlert(message, type) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.querySelector('.content-wrapper').insertBefore(alert, document.querySelector('.content-wrapper').firstChild);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
</script>
<?= $this->endSection() ?>
