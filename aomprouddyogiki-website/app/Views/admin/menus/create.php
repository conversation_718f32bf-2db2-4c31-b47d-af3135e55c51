<?= $this->extend('admin/layouts/admin') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-plus me-2"></i>
        Create New Menu Item
    </h2>
    <a href="<?= base_url('admin/menus') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        Back to Menus
    </a>
</div>

<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger">
        <h6>Please fix the following errors:</h6>
        <ul class="mb-0">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<form action="<?= base_url('admin/menus/create') ?>" method="POST">
    <?= csrf_field() ?>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Menu Item Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">Menu Title *</label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="<?= old('title') ?>" required>
                                <div class="form-text">The text that will appear in the menu.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="url" class="form-label">URL *</label>
                                <input type="text" class="form-control" id="url" name="url" 
                                       value="<?= old('url') ?>" required placeholder="/about">
                                <div class="form-text">Relative URL (e.g., /about) or full URL (e.g., https://example.com).</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="icon_class" class="form-label">Icon Class</label>
                                <input type="text" class="form-control" id="icon_class" name="icon_class" 
                                       value="<?= old('icon_class') ?>" placeholder="fas fa-home">
                                <div class="form-text">Font Awesome icon class (optional).</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="target" class="form-label">Link Target *</label>
                                <select class="form-select" id="target" name="target" required>
                                    <option value="_self" <?= old('target') == '_self' ? 'selected' : '' ?>>Same Window</option>
                                    <option value="_blank" <?= old('target') == '_blank' ? 'selected' : '' ?>>New Window/Tab</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">Parent Menu Item</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">None (Top Level)</option>
                                    <?php if (!empty($parentMenus)): ?>
                                        <?php foreach ($parentMenus as $parent): ?>
                                            <option value="<?= $parent['id'] ?>" <?= old('parent_id') == $parent['id'] ? 'selected' : '' ?>>
                                                <?= esc($parent['title']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                                <div class="form-text">Select a parent to create a dropdown menu item.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                       value="<?= old('sort_order', 0) ?>" min="0">
                                <div class="form-text">Higher numbers appear first in the menu.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Menu Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Menu Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="menu_location" class="form-label">Menu Location *</label>
                        <select class="form-select" id="menu_location" name="menu_location" required>
                            <option value="primary" <?= old('menu_location') == 'primary' ? 'selected' : '' ?>>Primary Navigation</option>
                            <option value="footer" <?= old('menu_location') == 'footer' ? 'selected' : '' ?>>Footer Menu</option>
                            <option value="sidebar" <?= old('menu_location') == 'sidebar' ? 'selected' : '' ?>>Sidebar Menu</option>
                        </select>
                        <div class="form-text">Choose where this menu item should appear.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Status *</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="active" <?= old('status') == 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= old('status') == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Quick Links -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">Quick URL Suggestions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setUrl('/')">
                            <i class="fas fa-home me-2"></i>Home
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setUrl('/about')">
                            <i class="fas fa-info-circle me-2"></i>About
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setUrl('/services')">
                            <i class="fas fa-cogs me-2"></i>Services
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setUrl('/products')">
                            <i class="fas fa-box me-2"></i>Products
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setUrl('/contact')">
                            <i class="fas fa-envelope me-2"></i>Contact
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="card mt-4">
                <div class="card-body">
                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-save me-2"></i>
                        Create Menu Item
                    </button>
                    <a href="<?= base_url('admin/menus') ?>" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-times me-2"></i>
                        Cancel
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Quick URL setter function
    function setUrl(url) {
        document.getElementById('url').value = url;
    }
    
    // Auto-suggest icon based on title
    document.getElementById('title').addEventListener('input', function() {
        const title = this.value.toLowerCase();
        const iconField = document.getElementById('icon_class');
        
        if (iconField.value === '') { // Only suggest if icon field is empty
            let suggestedIcon = '';
            
            if (title.includes('home')) suggestedIcon = 'fas fa-home';
            else if (title.includes('about')) suggestedIcon = 'fas fa-info-circle';
            else if (title.includes('service')) suggestedIcon = 'fas fa-cogs';
            else if (title.includes('product')) suggestedIcon = 'fas fa-box';
            else if (title.includes('contact')) suggestedIcon = 'fas fa-envelope';
            else if (title.includes('blog')) suggestedIcon = 'fas fa-blog';
            else if (title.includes('news')) suggestedIcon = 'fas fa-newspaper';
            else if (title.includes('gallery')) suggestedIcon = 'fas fa-images';
            else if (title.includes('portfolio')) suggestedIcon = 'fas fa-briefcase';
            
            if (suggestedIcon) {
                iconField.value = suggestedIcon;
                iconField.style.borderColor = '#28a745';
                setTimeout(() => {
                    iconField.style.borderColor = '';
                }, 2000);
            }
        }
    });
    
    // Preview icon
    document.getElementById('icon_class').addEventListener('input', function() {
        const iconClass = this.value;
        const preview = document.getElementById('icon_preview');
        
        if (preview) {
            preview.remove();
        }
        
        if (iconClass) {
            const previewElement = document.createElement('i');
            previewElement.className = iconClass + ' ms-2';
            previewElement.id = 'icon_preview';
            this.parentNode.appendChild(previewElement);
        }
    });
</script>
<?= $this->endSection() ?>
