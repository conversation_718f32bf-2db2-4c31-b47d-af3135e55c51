<?= $this->extend('admin/layouts/admin') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-bars me-2"></i>
        Manage Menus
    </h2>
    <a href="<?= base_url('admin/menus/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Create New Menu Item
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Navigation Menu Items</h5>
    </div>
    <div class="card-body">
        <?php if (!empty($menus)): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>URL</th>
                            <th>Location</th>
                            <th>Parent</th>
                            <th>Order</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="menuTable">
                        <?php 
                        $groupedMenus = [];
                        foreach ($menus as $menu) {
                            $groupedMenus[$menu['menu_location']][] = $menu;
                        }
                        ?>
                        
                        <?php foreach ($groupedMenus as $location => $locationMenus): ?>
                            <tr class="table-secondary">
                                <td colspan="7">
                                    <strong><?= esc(ucfirst($location)) ?> Menu</strong>
                                </td>
                            </tr>
                            <?php foreach ($locationMenus as $menu): ?>
                                <tr data-menu-id="<?= $menu['id'] ?>">
                                    <td>
                                        <?php if ($menu['parent_id']): ?>
                                            <span class="text-muted me-2">└─</span>
                                        <?php endif; ?>
                                        <strong><?= esc($menu['title']) ?></strong>
                                        <?php if ($menu['icon_class']): ?>
                                            <i class="<?= esc($menu['icon_class']) ?> ms-2"></i>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <code><?= esc($menu['url']) ?></code>
                                        <?php if ($menu['target'] == '_blank'): ?>
                                            <i class="fas fa-external-link-alt text-muted ms-1" title="Opens in new tab"></i>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?= esc(ucfirst($menu['menu_location'])) ?></span>
                                    </td>
                                    <td>
                                        <?php if ($menu['parent_id']): ?>
                                            <?php
                                            $parent = array_filter($menus, function($m) use ($menu) {
                                                return $m['id'] == $menu['parent_id'];
                                            });
                                            $parent = reset($parent);
                                            ?>
                                            <span class="text-muted"><?= esc($parent['title'] ?? 'Unknown') ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">—</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?= $menu['sort_order'] ?></span>
                                    </td>
                                    <td>
                                        <?php
                                        $statusColor = $menu['status'] == 'active' ? 'success' : 'secondary';
                                        ?>
                                        <span class="badge bg-<?= $statusColor ?>"><?= esc(ucfirst($menu['status'])) ?></span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('admin/menus/edit/' . $menu['id']) ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?= base_url('admin/menus/delete/' . $menu['id']) ?>" 
                                               class="btn btn-sm btn-outline-danger" 
                                               title="Delete"
                                               onclick="return confirm('Are you sure you want to delete this menu item?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="mt-3">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Menu Management Tips:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Use <strong>Sort Order</strong> to control menu item positioning (higher numbers appear first)</li>
                        <li>Set <strong>Parent</strong> to create dropdown/nested menu structures</li>
                        <li>Choose <strong>Location</strong> to place items in different menu areas</li>
                        <li>Use <strong>Target</strong> to control whether links open in new tabs</li>
                    </ul>
                </div>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-bars fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No menu items found</h5>
                <p class="text-muted">Create your first menu item to get started.</p>
                <a href="<?= base_url('admin/menus/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Create New Menu Item
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Future: Add drag-and-drop menu ordering functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Highlight parent-child relationships on hover
        const menuRows = document.querySelectorAll('[data-menu-id]');
        
        menuRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                const menuId = this.dataset.menuId;
                // Add visual highlighting for related menu items
                this.style.backgroundColor = '#f8f9fa';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
    });
</script>
<?= $this->endSection() ?>
