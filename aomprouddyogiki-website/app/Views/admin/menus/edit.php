<?= $this->extend('admin/layouts/admin') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-edit me-2"></i>
        Edit Menu Item: <?= esc($menu['title']) ?>
    </h2>
    <a href="<?= base_url('admin/menus') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        Back to Menus
    </a>
</div>

<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger">
        <h6>Please fix the following errors:</h6>
        <ul class="mb-0">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<form action="<?= base_url('admin/menus/edit/' . $menu['id']) ?>" method="POST">
    <?= csrf_field() ?>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Menu Item Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">Menu Title *</label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="<?= old('title', $menu['title']) ?>" required>
                                <div class="form-text">The text that will appear in the menu.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="url" class="form-label">URL *</label>
                                <input type="text" class="form-control" id="url" name="url" 
                                       value="<?= old('url', $menu['url']) ?>" required>
                                <div class="form-text">Relative URL (e.g., /about) or full URL (e.g., https://example.com).</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="icon_class" class="form-label">Icon Class</label>
                                <input type="text" class="form-control" id="icon_class" name="icon_class" 
                                       value="<?= old('icon_class', $menu['icon_class']) ?>" placeholder="fas fa-home">
                                <div class="form-text">Font Awesome icon class (optional).</div>
                                <?php if ($menu['icon_class']): ?>
                                    <div class="mt-2">
                                        <small class="text-muted">Current icon: </small>
                                        <i class="<?= esc($menu['icon_class']) ?>"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="target" class="form-label">Link Target *</label>
                                <select class="form-select" id="target" name="target" required>
                                    <option value="_self" <?= old('target', $menu['target']) == '_self' ? 'selected' : '' ?>>Same Window</option>
                                    <option value="_blank" <?= old('target', $menu['target']) == '_blank' ? 'selected' : '' ?>>New Window/Tab</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">Parent Menu Item</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">None (Top Level)</option>
                                    <?php if (!empty($parentMenus)): ?>
                                        <?php foreach ($parentMenus as $parent): ?>
                                            <option value="<?= $parent['id'] ?>" <?= old('parent_id', $menu['parent_id']) == $parent['id'] ? 'selected' : '' ?>>
                                                <?= esc($parent['title']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                                <div class="form-text">Select a parent to create a dropdown menu item.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                       value="<?= old('sort_order', $menu['sort_order']) ?>" min="0">
                                <div class="form-text">Higher numbers appear first in the menu.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Menu Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Menu Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="menu_location" class="form-label">Menu Location *</label>
                        <select class="form-select" id="menu_location" name="menu_location" required>
                            <option value="primary" <?= old('menu_location', $menu['menu_location']) == 'primary' ? 'selected' : '' ?>>Primary Navigation</option>
                            <option value="footer" <?= old('menu_location', $menu['menu_location']) == 'footer' ? 'selected' : '' ?>>Footer Menu</option>
                            <option value="sidebar" <?= old('menu_location', $menu['menu_location']) == 'sidebar' ? 'selected' : '' ?>>Sidebar Menu</option>
                        </select>
                        <div class="form-text">Choose where this menu item should appear.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Status *</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="active" <?= old('status', $menu['status']) == 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= old('status', $menu['status']) == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Quick Links -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">Quick URL Suggestions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setUrl('/')">
                            <i class="fas fa-home me-2"></i>Home
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setUrl('/about')">
                            <i class="fas fa-info-circle me-2"></i>About
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setUrl('/services')">
                            <i class="fas fa-cogs me-2"></i>Services
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setUrl('/products')">
                            <i class="fas fa-box me-2"></i>Products
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setUrl('/contact')">
                            <i class="fas fa-envelope me-2"></i>Contact
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="card mt-4">
                <div class="card-body">
                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-save me-2"></i>
                        Update Menu Item
                    </button>
                    <a href="<?= base_url('admin/menus') ?>" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-times me-2"></i>
                        Cancel
                    </a>
                </div>
            </div>
            
            <!-- Menu Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">Menu Information</h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <strong>Created:</strong> <?= date('M j, Y g:i A', strtotime($menu['created_at'])) ?><br>
                        <strong>Updated:</strong> <?= date('M j, Y g:i A', strtotime($menu['updated_at'])) ?><br>
                        <strong>ID:</strong> <?= $menu['id'] ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Quick URL setter function
    function setUrl(url) {
        document.getElementById('url').value = url;
    }
    
    // Preview icon changes
    document.getElementById('icon_class').addEventListener('input', function() {
        const iconClass = this.value;
        const preview = document.getElementById('icon_preview');
        
        if (preview) {
            preview.remove();
        }
        
        if (iconClass) {
            const previewElement = document.createElement('i');
            previewElement.className = iconClass + ' ms-2';
            previewElement.id = 'icon_preview';
            this.parentNode.appendChild(previewElement);
        }
    });
</script>
<?= $this->endSection() ?>
