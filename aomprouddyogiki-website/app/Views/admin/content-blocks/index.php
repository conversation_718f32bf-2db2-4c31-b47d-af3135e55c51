<?= $this->extend('admin/layouts/admin') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-th-large me-2"></i>
        Manage Content Blocks
    </h2>
    <a href="<?= base_url('admin/content-blocks/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Create New Content Block
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">All Content Blocks</h5>
    </div>
    <div class="card-body">
        <?php if (!empty($contentBlocks)): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Page</th>
                            <th>Type</th>
                            <th>Order</th>
                            <th>Status</th>
                            <th>Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($contentBlocks as $block): ?>
                            <tr>
                                <td>
                                    <strong><?= esc($block['title'] ?: 'Untitled Block') ?></strong>
                                    <?php if ($block['content']): ?>
                                        <br><small class="text-muted"><?= esc(substr(strip_tags($block['content']), 0, 60)) ?>...</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($block['page_title']): ?>
                                        <span class="badge bg-secondary"><?= esc($block['page_title']) ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">No page assigned</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $typeColors = [
                                        'hero' => 'primary',
                                        'content' => 'info',
                                        'features' => 'success',
                                        'testimonials' => 'warning',
                                        'gallery' => 'purple',
                                        'cta' => 'danger',
                                        'custom' => 'dark'
                                    ];
                                    $typeColor = $typeColors[$block['block_type']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?= $typeColor ?>"><?= esc(ucfirst($block['block_type'])) ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark"><?= $block['sort_order'] ?></span>
                                </td>
                                <td>
                                    <?php
                                    $statusColor = $block['status'] == 'active' ? 'success' : 'secondary';
                                    ?>
                                    <span class="badge bg-<?= $statusColor ?>"><?= esc(ucfirst($block['status'])) ?></span>
                                </td>
                                <td>
                                    <?= date('M j, Y', strtotime($block['updated_at'])) ?><br>
                                    <small class="text-muted"><?= date('g:i A', strtotime($block['updated_at'])) ?></small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('admin/content-blocks/edit/' . $block['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?= base_url('admin/content-blocks/duplicate/' . $block['id']) ?>" 
                                           class="btn btn-sm btn-outline-info" 
                                           title="Duplicate">
                                            <i class="fas fa-copy"></i>
                                        </a>
                                        <a href="<?= base_url('admin/content-blocks/delete/' . $block['id']) ?>" 
                                           class="btn btn-sm btn-outline-danger" 
                                           title="Delete"
                                           onclick="return confirm('Are you sure you want to delete this content block?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-th-large fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No content blocks found</h5>
                <p class="text-muted">Create your first content block to get started.</p>
                <a href="<?= base_url('admin/content-blocks/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Create New Content Block
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Content Block Types Info -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Content Block Types</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <span class="badge bg-primary me-2">Hero</span>
                                Large banner sections with images and call-to-action buttons
                            </li>
                            <li class="mb-2">
                                <span class="badge bg-info me-2">Content</span>
                                Regular text content with rich formatting
                            </li>
                            <li class="mb-2">
                                <span class="badge bg-success me-2">Features</span>
                                Feature lists and service highlights
                            </li>
                            <li class="mb-2">
                                <span class="badge bg-warning me-2">Testimonials</span>
                                Customer reviews and testimonials
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <span class="badge bg-purple me-2">Gallery</span>
                                Image galleries and portfolios
                            </li>
                            <li class="mb-2">
                                <span class="badge bg-danger me-2">CTA</span>
                                Call-to-action sections and buttons
                            </li>
                            <li class="mb-2">
                                <span class="badge bg-dark me-2">Custom</span>
                                Custom HTML content and special layouts
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('head') ?>
<style>
    .bg-purple {
        background-color: #6f42c1 !important;
    }
</style>
<?= $this->endSection() ?>
