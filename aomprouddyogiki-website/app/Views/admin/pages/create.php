<?= $this->extend('admin/layouts/admin') ?>

<?= $this->section('head') ?>
<!-- Quill.js Editor -->
<link href="https://cdn.quilljs.com/1.3.7/quill.snow.css" rel="stylesheet">
<script src="https://cdn.quilljs.com/1.3.7/quill.min.js"></script>
<!-- CodeMirror for HTML Source Editor -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closetag.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/xml-fold.min.js"></script>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-plus me-2"></i>
        Create New Page
    </h2>
    <a href="<?= base_url('admin/pages') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        Back to Pages
    </a>
</div>

<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger">
        <h6>Please fix the following errors:</h6>
        <ul class="mb-0">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<form action="<?= base_url('admin/pages/create') ?>" method="POST">
    <?= csrf_field() ?>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Page Content</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">Page Title *</label>
                        <input type="text" class="form-control" id="title" name="title" 
                               value="<?= old('title') ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="slug" class="form-label">Page Slug *</label>
                        <input type="text" class="form-control" id="slug" name="slug" 
                               value="<?= old('slug') ?>" required>
                        <div class="form-text">URL-friendly version of the title. Leave blank to auto-generate.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="excerpt" class="form-label">Page Excerpt</label>
                        <textarea class="form-control" id="excerpt" name="excerpt" rows="3"><?= old('excerpt') ?></textarea>
                        <div class="form-text">Brief description of the page content.</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label for="content" class="form-label mb-0">Page Content</label>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary active" id="wysiwyg-btn">
                                    <i class="fas fa-eye me-1"></i> WYSIWYG
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="html-btn">
                                    <i class="fas fa-code me-1"></i> HTML
                                </button>
                                <button type="button" class="btn btn-outline-success" id="image-btn">
                                    <i class="fas fa-image me-1"></i> Images
                                </button>
                            </div>
                        </div>
                        <div id="content-editor" style="height: 400px;"><?= old('content') ?></div>
                        <div id="html-editor" style="height: 400px; display: none;"></div>
                        <textarea id="content" name="content" style="display: none;"><?= old('content') ?></textarea>
                    </div>
                </div>
            </div>
            
            <!-- Hero Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Hero Section</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hero_title" class="form-label">Hero Title</label>
                                <input type="text" class="form-control" id="hero_title" name="hero_title" 
                                       value="<?= old('hero_title') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hero_image" class="form-label">Hero Image</label>
                                <input type="text" class="form-control" id="hero_image" name="hero_image" 
                                       value="<?= old('hero_image') ?>" placeholder="images/hero-image.jpg">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="hero_subtitle" class="form-label">Hero Subtitle</label>
                        <textarea class="form-control" id="hero_subtitle" name="hero_subtitle" rows="2"><?= old('hero_subtitle') ?></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hero_cta_text" class="form-label">CTA Button Text</label>
                                <input type="text" class="form-control" id="hero_cta_text" name="hero_cta_text" 
                                       value="<?= old('hero_cta_text') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hero_cta_link" class="form-label">CTA Button Link</label>
                                <input type="text" class="form-control" id="hero_cta_link" name="hero_cta_link" 
                                       value="<?= old('hero_cta_link') ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Page Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Page Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status *</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="draft" <?= old('status') == 'draft' ? 'selected' : '' ?>>Draft</option>
                            <option value="published" <?= old('status') == 'published' ? 'selected' : '' ?>>Published</option>
                            <option value="archived" <?= old('status') == 'archived' ? 'selected' : '' ?>>Archived</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="template" class="form-label">Template *</label>
                        <select class="form-select" id="template" name="template" required>
                            <option value="default" <?= old('template') == 'default' ? 'selected' : '' ?>>Default</option>
                            <option value="service" <?= old('template') == 'service' ? 'selected' : '' ?>>Service</option>
                            <option value="product" <?= old('template') == 'product' ? 'selected' : '' ?>>Product</option>
                            <option value="about" <?= old('template') == 'about' ? 'selected' : '' ?>>About</option>
                            <option value="contact" <?= old('template') == 'contact' ? 'selected' : '' ?>>Contact</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">Sort Order</label>
                        <input type="number" class="form-control" id="sort_order" name="sort_order" 
                               value="<?= old('sort_order', 0) ?>" min="0">
                        <div class="form-text">Higher numbers appear first.</div>
                    </div>
                </div>
            </div>
            
            <!-- SEO Settings -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">SEO Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="meta_title" class="form-label">Meta Title</label>
                        <input type="text" class="form-control" id="meta_title" name="meta_title" 
                               value="<?= old('meta_title') ?>" maxlength="255">
                        <div class="form-text">Leave blank to use page title.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="meta_description" class="form-label">Meta Description</label>
                        <textarea class="form-control" id="meta_description" name="meta_description" 
                                  rows="3" maxlength="500"><?= old('meta_description') ?></textarea>
                        <div class="form-text">Recommended: 150-160 characters.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">Meta Keywords</label>
                        <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" 
                               value="<?= old('meta_keywords') ?>">
                        <div class="form-text">Comma-separated keywords.</div>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="card mt-4">
                <div class="card-body">
                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-save me-2"></i>
                        Create Page
                    </button>
                    <a href="<?= base_url('admin/pages') ?>" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-times me-2"></i>
                        Cancel
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let currentEditor = 'wysiwyg';
    let quill, htmlEditor;

    // Initialize Quill.js
    quill = new Quill('#content-editor', {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'align': [] }],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'indent': '-1'}, { 'indent': '+1' }],
                ['link', 'image', 'video'],
                ['blockquote', 'code-block'],
                ['clean']
            ]
        },
        placeholder: 'Enter page content here...'
    });

    // Initialize CodeMirror HTML Editor
    htmlEditor = CodeMirror(document.getElementById('html-editor'), {
        mode: 'htmlmixed',
        theme: 'monokai',
        lineNumbers: true,
        autoCloseTags: true,
        lineWrapping: true,
        indentUnit: 2,
        tabSize: 2,
        value: document.getElementById('content').value || ''
    });

    // Sync Quill content with hidden textarea
    quill.on('text-change', function() {
        if (currentEditor === 'wysiwyg') {
            const content = quill.root.innerHTML;
            document.getElementById('content').value = content;
            htmlEditor.setValue(content);
        }
    });

    // Sync HTML editor content with hidden textarea
    htmlEditor.on('change', function() {
        if (currentEditor === 'html') {
            const content = htmlEditor.getValue();
            document.getElementById('content').value = content;
            quill.root.innerHTML = content;
        }
    });

    // Initialize content if editing
    if (document.getElementById('content').value) {
        const content = document.getElementById('content').value;
        quill.root.innerHTML = content;
        htmlEditor.setValue(content);
    }

    // Editor toggle functionality
    document.getElementById('wysiwyg-btn').addEventListener('click', function() {
        if (currentEditor !== 'wysiwyg') {
            // Sync from HTML to WYSIWYG
            const htmlContent = htmlEditor.getValue();
            quill.root.innerHTML = htmlContent;
            document.getElementById('content').value = htmlContent;

            // Toggle visibility
            document.getElementById('content-editor').style.display = 'block';
            document.getElementById('html-editor').style.display = 'none';

            // Toggle button states
            this.classList.add('active');
            document.getElementById('html-btn').classList.remove('active');

            currentEditor = 'wysiwyg';
        }
    });

    document.getElementById('html-btn').addEventListener('click', function() {
        if (currentEditor !== 'html') {
            // Sync from WYSIWYG to HTML
            const quillContent = quill.root.innerHTML;
            htmlEditor.setValue(quillContent);
            document.getElementById('content').value = quillContent;

            // Toggle visibility
            document.getElementById('content-editor').style.display = 'none';
            document.getElementById('html-editor').style.display = 'block';
            htmlEditor.refresh(); // Refresh CodeMirror when shown

            // Toggle button states
            this.classList.add('active');
            document.getElementById('wysiwyg-btn').classList.remove('active');

            currentEditor = 'html';
        }
    });
    
    // Auto-generate slug from title
    document.getElementById('title').addEventListener('input', function() {
        const title = this.value;
        const slug = title.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');
        document.getElementById('slug').value = slug;
    });
    
    // Character counter for meta description
    const metaDesc = document.getElementById('meta_description');
    const counter = document.createElement('div');
    counter.className = 'form-text text-end';
    metaDesc.parentNode.appendChild(counter);
    
    function updateCounter() {
        const length = metaDesc.value.length;
        counter.textContent = `${length}/500 characters`;
        counter.className = length > 160 ? 'form-text text-end text-warning' : 'form-text text-end';
    }
    
    metaDesc.addEventListener('input', updateCounter);
    updateCounter();
</script>
<?= $this->endSection() ?>
