<?= $this->extend('admin/layouts/admin') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-file-alt me-2"></i>
        Manage Pages
    </h2>
    <a href="<?= base_url('admin/pages/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Create New Page
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">All Pages</h5>
    </div>
    <div class="card-body">
        <?php if (!empty($pages)): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Slug</th>
                            <th>Template</th>
                            <th>Status</th>
                            <th>Author</th>
                            <th>Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pages as $page): ?>
                            <tr>
                                <td>
                                    <strong><?= esc($page['title']) ?></strong>
                                    <?php if ($page['excerpt']): ?>
                                        <br><small class="text-muted"><?= esc(substr($page['excerpt'], 0, 60)) ?>...</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <code><?= esc($page['slug']) ?></code>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?= esc(ucfirst($page['template'])) ?></span>
                                </td>
                                <td>
                                    <?php
                                    $statusColors = [
                                        'draft' => 'warning',
                                        'published' => 'success',
                                        'archived' => 'secondary'
                                    ];
                                    $statusColor = $statusColors[$page['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?= $statusColor ?>"><?= esc(ucfirst($page['status'])) ?></span>
                                </td>
                                <td>
                                    <?= esc($page['author_name'] ?? 'Unknown') ?>
                                </td>
                                <td>
                                    <?= date('M j, Y', strtotime($page['updated_at'])) ?><br>
                                    <small class="text-muted"><?= date('g:i A', strtotime($page['updated_at'])) ?></small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('admin/pages/preview/' . $page['id']) ?>" 
                                           class="btn btn-sm btn-outline-info" 
                                           target="_blank" 
                                           title="Preview">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('admin/pages/edit/' . $page['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?= base_url('admin/pages/delete/' . $page['id']) ?>" 
                                           class="btn btn-sm btn-outline-danger" 
                                           title="Delete"
                                           onclick="return confirm('Are you sure you want to delete this page?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No pages found</h5>
                <p class="text-muted">Create your first page to get started.</p>
                <a href="<?= base_url('admin/pages/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    Create New Page
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Auto-refresh page status every 30 seconds
    setTimeout(function() {
        location.reload();
    }, 30000);
</script>
<?= $this->endSection() ?>
