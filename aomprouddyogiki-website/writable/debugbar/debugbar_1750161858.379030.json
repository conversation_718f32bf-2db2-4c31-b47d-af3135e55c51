{"url": "http://localhost:8000/index.php/admin/dashboard", "method": "GET", "isAJAX": false, "startTime": **********.26042, "totalTime": 87.9, "totalMemory": "7.314", "segmentDuration": 15, "segmentCount": 6, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.265462, "duration": 0.032525062561035156}, {"name": "Required Before Filters", "component": "Timer", "start": **********.297989, "duration": 0.002512216567993164}, {"name": "Routing", "component": "Timer", "start": **********.300508, "duration": 0.0010399818420410156}, {"name": "Before Filters", "component": "Timer", "start": **********.30174, "duration": 3.314018249511719e-05}, {"name": "Controller", "component": "Timer", "start": **********.301776, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.301777, "duration": 0.021000146865844727}, {"name": "After Filters", "component": "Timer", "start": **********.347349, "duration": 5.0067901611328125e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.347398, "duration": 0.0009479522705078125}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(20 total Queries, 20 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.56 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `cms_pages`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1678", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:928", "function": "        CodeIgniter\\Database\\BaseBuilder->countAll()", "index": "  2    "}, {"file": "APPPATH/Models/CmsPageModel.php:156", "function": "        CodeIgniter\\Model->__call()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:98", "function": "        App\\Models\\CmsPageModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsPageModel.php:156", "qid": "73013acf47f32b0ec581c643dc6a0b09"}, {"hover": "", "class": "", "duration": "0.54 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_pages`\n<strong>WHERE</strong> `status` = &#039;published&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsPageModel.php:157", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:98", "function": "        App\\Models\\CmsPageModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsPageModel.php:157", "qid": "ad427c3c07370f007ae5e40d9723c8ca"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_pages`\n<strong>WHERE</strong> `status` = &#039;published&#039;\n<strong>AND</strong> `status` = &#039;draft&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsPageModel.php:158", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:98", "function": "        App\\Models\\CmsPageModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsPageModel.php:158", "qid": "475dc9a15c089f676927384138734654"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_pages`\n<strong>WHERE</strong> `status` = &#039;published&#039;\n<strong>AND</strong> `status` = &#039;draft&#039;\n<strong>AND</strong> `status` = &#039;archived&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsPageModel.php:159", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:98", "function": "        App\\Models\\CmsPageModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsPageModel.php:159", "qid": "3f82ef55695851904eaa907a8cb0333e"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `cms_menus`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1678", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:928", "function": "        CodeIgniter\\Database\\BaseBuilder->countAll()", "index": "  2    "}, {"file": "APPPATH/Models/CmsMenuModel.php:146", "function": "        CodeIgniter\\Model->__call()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:99", "function": "        App\\Models\\CmsMenuModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsMenuModel.php:146", "qid": "92c1747115cd88601f2cc765997c043d"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_menus`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsMenuModel.php:147", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:99", "function": "        App\\Models\\CmsMenuModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsMenuModel.php:147", "qid": "f89971222a0bc5ec2d27dce2d8e4591f"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_menus`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsMenuModel.php:148", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:99", "function": "        App\\Models\\CmsMenuModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsMenuModel.php:148", "qid": "9b6ef438517b7ef59983ab7008a10bc7"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_menus`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `menu_location` = &#039;primary&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsMenuModel.php:149", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:99", "function": "        App\\Models\\CmsMenuModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsMenuModel.php:149", "qid": "ab10230123071a1a9578a21c95b1725b"}, {"hover": "", "class": "", "duration": "0.4 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_menus`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `menu_location` = &#039;primary&#039;\n<strong>AND</strong> `menu_location` = &#039;footer&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsMenuModel.php:150", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:99", "function": "        App\\Models\\CmsMenuModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsMenuModel.php:150", "qid": "4080830f49a66596d5d1f9da849ac4bb"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `cms_content_blocks`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1678", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:928", "function": "        CodeIgniter\\Database\\BaseBuilder->countAll()", "index": "  2    "}, {"file": "APPPATH/Models/CmsContentBlockModel.php:149", "function": "        CodeIgniter\\Model->__call()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:100", "function": "        App\\Models\\CmsContentBlockModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsContentBlockModel.php:149", "qid": "c2f40bf5965541fef4a084ffae39d82b"}, {"hover": "", "class": "", "duration": "0.37 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsContentBlockModel.php:150", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:100", "function": "        App\\Models\\CmsContentBlockModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsContentBlockModel.php:150", "qid": "dc47c535f573ba75b139b3e166a08356"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsContentBlockModel.php:151", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:100", "function": "        App\\Models\\CmsContentBlockModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsContentBlockModel.php:151", "qid": "fa85aee9105a01401ba9f2da2601ff73"}, {"hover": "", "class": "", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsContentBlockModel.php:156", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:100", "function": "        App\\Models\\CmsContentBlockModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsContentBlockModel.php:156", "qid": "edbca0448721a61df9ded35ce254aafb"}, {"hover": "", "class": "", "duration": "0.37 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;\n<strong>AND</strong> `block_type` = &#039;content&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsContentBlockModel.php:156", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:100", "function": "        App\\Models\\CmsContentBlockModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsContentBlockModel.php:156", "qid": "efc6af092cb9eb33ef314b6f27ce6f7a"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;\n<strong>AND</strong> `block_type` = &#039;content&#039;\n<strong>AND</strong> `block_type` = &#039;features&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsContentBlockModel.php:156", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:100", "function": "        App\\Models\\CmsContentBlockModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsContentBlockModel.php:156", "qid": "04b45f205f70183d7ebe88a1d25e6af4"}, {"hover": "", "class": "", "duration": "0.36 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;\n<strong>AND</strong> `block_type` = &#039;content&#039;\n<strong>AND</strong> `block_type` = &#039;features&#039;\n<strong>AND</strong> `block_type` = &#039;testimonials&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsContentBlockModel.php:156", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:100", "function": "        App\\Models\\CmsContentBlockModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsContentBlockModel.php:156", "qid": "7c617a0b0cf5f610d594cb30637dff67"}, {"hover": "", "class": "", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;\n<strong>AND</strong> `block_type` = &#039;content&#039;\n<strong>AND</strong> `block_type` = &#039;features&#039;\n<strong>AND</strong> `block_type` = &#039;testimonials&#039;\n<strong>AND</strong> `block_type` = &#039;gallery&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsContentBlockModel.php:156", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:100", "function": "        App\\Models\\CmsContentBlockModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsContentBlockModel.php:156", "qid": "43d679d75c558dcfb9446d9ff8a53fe9"}, {"hover": "", "class": "", "duration": "0.4 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;\n<strong>AND</strong> `block_type` = &#039;content&#039;\n<strong>AND</strong> `block_type` = &#039;features&#039;\n<strong>AND</strong> `block_type` = &#039;testimonials&#039;\n<strong>AND</strong> `block_type` = &#039;gallery&#039;\n<strong>AND</strong> `block_type` = &#039;cta&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsContentBlockModel.php:156", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:100", "function": "        App\\Models\\CmsContentBlockModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsContentBlockModel.php:156", "qid": "e6028506c270abfb7667cb2ba2ae9e42"}, {"hover": "", "class": "", "duration": "0.46 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;\n<strong>AND</strong> `block_type` = &#039;content&#039;\n<strong>AND</strong> `block_type` = &#039;features&#039;\n<strong>AND</strong> `block_type` = &#039;testimonials&#039;\n<strong>AND</strong> `block_type` = &#039;gallery&#039;\n<strong>AND</strong> `block_type` = &#039;cta&#039;\n<strong>AND</strong> `block_type` = &#039;custom&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/CmsContentBlockModel.php:156", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Admin.php:100", "function": "        App\\Models\\CmsContentBlockModel->getStatistics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CmsContentBlockModel.php:156", "qid": "597dd0d2b275d1b9fe4f55c780e9dfc9"}, {"hover": "", "class": "", "duration": "0.41 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `contacts`\n<strong>ORDER</strong> <strong>BY</strong> `created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/ContactModel.php:98", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Admin.php:101", "function": "        App\\Models\\ContactModel->getRecent()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin->dashboard()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/ContactModel.php:98", "qid": "741b6ef705c705b7b3c20c45fde5ffeb"}]}, "badgeValue": 20, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.330686, "duration": "0.000851"}, {"name": "Query", "component": "Database", "start": **********.332326, "duration": "0.000564", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `cms_pages`"}, {"name": "Query", "component": "Database", "start": **********.334955, "duration": "0.000539", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_pages`\n<strong>WHERE</strong> `status` = &#039;published&#039;"}, {"name": "Query", "component": "Database", "start": **********.335633, "duration": "0.000273", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_pages`\n<strong>WHERE</strong> `status` = &#039;published&#039;\n<strong>AND</strong> `status` = &#039;draft&#039;"}, {"name": "Query", "component": "Database", "start": **********.336007, "duration": "0.000268", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_pages`\n<strong>WHERE</strong> `status` = &#039;published&#039;\n<strong>AND</strong> `status` = &#039;draft&#039;\n<strong>AND</strong> `status` = &#039;archived&#039;"}, {"name": "Query", "component": "Database", "start": **********.336351, "duration": "0.000252", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `cms_menus`"}, {"name": "Query", "component": "Database", "start": **********.336716, "duration": "0.000300", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_menus`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.337114, "duration": "0.000261", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_menus`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;"}, {"name": "Query", "component": "Database", "start": **********.33747, "duration": "0.000248", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_menus`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `menu_location` = &#039;primary&#039;"}, {"name": "Query", "component": "Database", "start": **********.337832, "duration": "0.000395", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_menus`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `menu_location` = &#039;primary&#039;\n<strong>AND</strong> `menu_location` = &#039;footer&#039;"}, {"name": "Query", "component": "Database", "start": **********.338308, "duration": "0.000317", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `cms_content_blocks`"}, {"name": "Query", "component": "Database", "start": **********.338793, "duration": "0.000370", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.339321, "duration": "0.000288", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;"}, {"name": "Query", "component": "Database", "start": **********.339725, "duration": "0.000384", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;"}, {"name": "Query", "component": "Database", "start": **********.34029, "duration": "0.000366", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;\n<strong>AND</strong> `block_type` = &#039;content&#039;"}, {"name": "Query", "component": "Database", "start": **********.340808, "duration": "0.000319", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;\n<strong>AND</strong> `block_type` = &#039;content&#039;\n<strong>AND</strong> `block_type` = &#039;features&#039;"}, {"name": "Query", "component": "Database", "start": **********.341292, "duration": "0.000358", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;\n<strong>AND</strong> `block_type` = &#039;content&#039;\n<strong>AND</strong> `block_type` = &#039;features&#039;\n<strong>AND</strong> `block_type` = &#039;testimonials&#039;"}, {"name": "Query", "component": "Database", "start": **********.341809, "duration": "0.000376", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;\n<strong>AND</strong> `block_type` = &#039;content&#039;\n<strong>AND</strong> `block_type` = &#039;features&#039;\n<strong>AND</strong> `block_type` = &#039;testimonials&#039;\n<strong>AND</strong> `block_type` = &#039;gallery&#039;"}, {"name": "Query", "component": "Database", "start": **********.342346, "duration": "0.000401", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;\n<strong>AND</strong> `block_type` = &#039;content&#039;\n<strong>AND</strong> `block_type` = &#039;features&#039;\n<strong>AND</strong> `block_type` = &#039;testimonials&#039;\n<strong>AND</strong> `block_type` = &#039;gallery&#039;\n<strong>AND</strong> `block_type` = &#039;cta&#039;"}, {"name": "Query", "component": "Database", "start": **********.342932, "duration": "0.000456", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `cms_content_blocks`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `block_type` = &#039;hero&#039;\n<strong>AND</strong> `block_type` = &#039;content&#039;\n<strong>AND</strong> `block_type` = &#039;features&#039;\n<strong>AND</strong> `block_type` = &#039;testimonials&#039;\n<strong>AND</strong> `block_type` = &#039;gallery&#039;\n<strong>AND</strong> `block_type` = &#039;cta&#039;\n<strong>AND</strong> `block_type` = &#039;custom&#039;"}, {"name": "Query", "component": "Database", "start": **********.343601, "duration": "0.000413", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `contacts`\n<strong>ORDER</strong> <strong>BY</strong> `created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: admin/layouts/admin.php", "component": "Views", "start": **********.346422, "duration": 0.0008630752563476562}, {"name": "View: admin/dashboard.php", "component": "Views", "start": **********.345827, "duration": 0.001489877700805664}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 165 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Log/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Log/Handlers/HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/Admin.php", "name": "Admin.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Models/CmsAdminModel.php", "name": "CmsAdminModel.php"}, {"path": "APPPATH/Models/CmsContentBlockModel.php", "name": "CmsContentBlockModel.php"}, {"path": "APPPATH/Models/CmsMenuModel.php", "name": "CmsMenuModel.php"}, {"path": "APPPATH/Models/CmsPageModel.php", "name": "CmsPageModel.php"}, {"path": "APPPATH/Models/ContactModel.php", "name": "ContactModel.php"}, {"path": "APPPATH/Views/admin/dashboard.php", "name": "dashboard.php"}, {"path": "APPPATH/Views/admin/layouts/admin.php", "name": "admin.php"}, {"path": "FCPATH/index.php", "name": "index.php"}, {"path": "VENDORPATH/autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH/composer/ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH/composer/InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH/composer/autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH/composer/autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH/composer/installed.php", "name": "installed.php"}, {"path": "VENDORPATH/composer/platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH/laminas/laminas-escaper/src/Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH/laminas/laminas-escaper/src/EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH/myclabs/deep-copy/src/DeepCopy/deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH/phpunit/phpunit/src/Framework/Assert/Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH/psr/log/src/LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH/psr/log/src/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH/psr/log/src/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH/symfony/deprecation-contracts/function.php", "name": "function.php"}]}, "badgeValue": 165, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Admin", "method": "dashboard", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Home::about"}, {"method": "GET", "route": "services", "handler": "\\App\\Controllers\\Services::index"}, {"method": "GET", "route": "services/web-development", "handler": "\\App\\Controllers\\Services::webDevelopment"}, {"method": "GET", "route": "services/mobile-development", "handler": "\\App\\Controllers\\Services::mobileDevelopment"}, {"method": "GET", "route": "services/ecommerce-development", "handler": "\\App\\Controllers\\Services::ecommerceDevelopment"}, {"method": "GET", "route": "services/cms-development", "handler": "\\App\\Controllers\\Services::cmsDevelopment"}, {"method": "GET", "route": "services/api-development", "handler": "\\App\\Controllers\\Services::apiDevelopment"}, {"method": "GET", "route": "services/hosting", "handler": "\\App\\Controllers\\Services::hosting"}, {"method": "GET", "route": "services/digital-marketing", "handler": "\\App\\Controllers\\Services::digitalMarketing"}, {"method": "GET", "route": "services/maintenance-support", "handler": "\\App\\Controllers\\Services::maintenanceSupport"}, {"method": "GET", "route": "products", "handler": "\\App\\Controllers\\Products::index"}, {"method": "GET", "route": "products/govt-exam-prep", "handler": "\\App\\Controllers\\Products::govtExamPrep"}, {"method": "GET", "route": "contact", "handler": "\\App\\Controllers\\Contact::index"}, {"method": "GET", "route": "admin/login", "handler": "\\App\\Controllers\\Admin::login"}, {"method": "GET", "route": "admin/logout", "handler": "\\App\\Controllers\\Admin::logout"}, {"method": "GET", "route": "admin/dashboard", "handler": "\\App\\Controllers\\Admin::dashboard"}, {"method": "GET", "route": "admin", "handler": "\\App\\Controllers\\Admin::dashboard"}, {"method": "GET", "route": "admin/pages", "handler": "\\App\\Controllers\\CmsPages::index"}, {"method": "GET", "route": "admin/pages/create", "handler": "\\App\\Controllers\\CmsPages::create"}, {"method": "GET", "route": "admin/pages/edit/([0-9]+)", "handler": "\\App\\Controllers\\CmsPages::edit/$1"}, {"method": "GET", "route": "admin/pages/delete/([0-9]+)", "handler": "\\App\\Controllers\\CmsPages::delete/$1"}, {"method": "GET", "route": "admin/pages/preview/([0-9]+)", "handler": "\\App\\Controllers\\CmsPages::preview/$1"}, {"method": "GET", "route": "admin/menus", "handler": "\\App\\Controllers\\CmsMenus::index"}, {"method": "GET", "route": "admin/menus/create", "handler": "\\App\\Controllers\\CmsMenus::create"}, {"method": "GET", "route": "admin/menus/edit/([0-9]+)", "handler": "\\App\\Controllers\\CmsMenus::edit/$1"}, {"method": "GET", "route": "admin/menus/delete/([0-9]+)", "handler": "\\App\\Controllers\\CmsMenus::delete/$1"}, {"method": "GET", "route": "admin/content-blocks", "handler": "\\App\\Controllers\\CmsContentBlocks::index"}, {"method": "GET", "route": "admin/content-blocks/create", "handler": "\\App\\Controllers\\CmsContentBlocks::create"}, {"method": "GET", "route": "admin/content-blocks/edit/([0-9]+)", "handler": "\\App\\Controllers\\CmsContentBlocks::edit/$1"}, {"method": "GET", "route": "admin/content-blocks/delete/([0-9]+)", "handler": "\\App\\Controllers\\CmsContentBlocks::delete/$1"}, {"method": "GET", "route": "services/([^/]+)", "handler": "\\App\\Controllers\\CmsPage::viewBySlug/services/$1"}, {"method": "GET", "route": "products/([^/]+)", "handler": "\\App\\Controllers\\CmsPage::viewBySlug/products/$1"}, {"method": "GET", "route": "cms/([^/]+)", "handler": "\\App\\Controllers\\CmsPage::view/$1"}, {"method": "GET", "route": "([^/]+)", "handler": "\\App\\Controllers\\CmsPage::viewBySlug/$1"}, {"method": "GET", "route": "api/services", "handler": "\\App\\Controllers\\Api\\ApiServices::index"}, {"method": "GET", "route": "api/products", "handler": "\\App\\Controllers\\Api\\ApiProducts::index"}, {"method": "GET", "route": "api/company-info", "handler": "\\App\\Controllers\\Api\\ApiCompany::info"}, {"method": "POST", "route": "contact/submit", "handler": "\\App\\Controllers\\Contact::submit"}, {"method": "POST", "route": "admin/authenticate", "handler": "\\App\\Controllers\\Admin::authenticate"}, {"method": "POST", "route": "admin/pages/create", "handler": "\\App\\Controllers\\CmsPages::store"}, {"method": "POST", "route": "admin/pages/edit/([0-9]+)", "handler": "\\App\\Controllers\\CmsPages::update/$1"}, {"method": "POST", "route": "admin/menus/create", "handler": "\\App\\Controllers\\CmsMenus::store"}, {"method": "POST", "route": "admin/menus/edit/([0-9]+)", "handler": "\\App\\Controllers\\CmsMenus::update/$1"}, {"method": "POST", "route": "admin/menus/update-order", "handler": "\\App\\Controllers\\CmsMenus::updateOrder"}, {"method": "POST", "route": "admin/content-blocks/create", "handler": "\\App\\Controllers\\CmsContentBlocks::store"}, {"method": "POST", "route": "admin/content-blocks/edit/([0-9]+)", "handler": "\\App\\Controllers\\CmsContentBlocks::update/$1"}, {"method": "POST", "route": "admin/content-blocks/update-order", "handler": "\\App\\Controllers\\CmsContentBlocks::updateOrder"}, {"method": "POST", "route": "api/contact", "handler": "\\App\\Controllers\\Api\\ApiContact::submit"}]}, "badgeValue": 39, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "11.76", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.50", "count": 20}}}, "badgeValue": 21, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.286222, "duration": 0.011755943298339844}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.332898, "duration": 3.814697265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.335499, "duration": 2.7894973754882812e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.33591, "duration": 1.5974044799804688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.336278, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.336606, "duration": 1.5974044799804688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.337019, "duration": 1.5974044799804688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.337378, "duration": 1.5974044799804688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.337721, "duration": 1.6927719116210938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.33823, "duration": 1.9073486328125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.338629, "duration": 2.002716064453125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.339169, "duration": 2.5033950805664062e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.339613, "duration": 2.09808349609375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.340116, "duration": 3.2901763916015625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.34066, "duration": 2.5987625122070312e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.341132, "duration": 2.5033950805664062e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.341656, "duration": 2.6941299438476562e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.342189, "duration": 3.981590270996094e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.342755, "duration": 3.886222839355469e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.343394, "duration": 3.3855438232421875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.344021, "duration": 3.2901763916015625e-05}]}], "vars": {"varData": {"View Data": {"title": "Admin Dashboard - AomProuddyogiki CMS", "meta_description": "Admin dashboard for AomProuddyogiki CMS", "pageStats": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (4)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total</dfn> =&gt; <var>integer</var> 14<div class=\"access-path\">$value['total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>published</dfn> =&gt; <var>integer</var> 14<div class=\"access-path\">$value['published']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>draft</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['draft']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>archived</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['archived']</div></dt></dl></dd></dl></div>", "menuStats": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total</dfn> =&gt; <var>integer</var> 14<div class=\"access-path\">$value['total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>active</dfn> =&gt; <var>integer</var> 14<div class=\"access-path\">$value['active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>inactive</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['inactive']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>primary</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['primary']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>footer</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['footer']</div></dt></dl></dd></dl></div>", "blockStats": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (10)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['total']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>active</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['active']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>inactive</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['inactive']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>hero</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['hero']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>content</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['content']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>features</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['features']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>testimonials</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['testimonials']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gallery</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['gallery']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>cta</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['cta']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>custom</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['custom']</div></dt></dl></dd></dl></div>", "recentContacts": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>array</var> (0)</dt></dl></div>", "admin": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>username</dfn> =&gt; <var>string</var> (5) \"admin\"<div class=\"access-path\">$value['username']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (25) \"<EMAIL>\"<div class=\"access-path\">$value['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>full_name</dfn> =&gt; <var>string</var> (20) \"System Administrator\"<div class=\"access-path\">$value['full_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>role</dfn> =&gt; <var>string</var> (11) \"super_admin\"<div class=\"access-path\">$value['role']</div></dt></dl></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>**********</pre>", "_ci_previous_url": "http://localhost:8000/index.php/admin/dashboard", "admin_id": "1", "admin_username": "admin", "admin_email": "<EMAIL>", "admin_full_name": "System Administrator", "admin_role": "super_admin", "admin_logged_in": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Host": "localhost:8000", "Connection": "keep-alive", "Cache-Control": "max-age=0", "Sec-Ch-Ua": "&quot;Brave&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Linux&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8", "Sec-Gpc": "1", "Accept-Language": "en-US,en;q=0.8", "Sec-Fetch-Site": "cross-site", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Accept-Encoding": "gzip, deflate, br, zstd", "Cookie": "admin_auth=eyJpdiI6IkQ3T3RUQngyc043Q3RZSWVnSjhWaFE9PSIsInZhbHVlIjoiRTlGaDVKSTMxdm15ODBTM2tvZFdQRldxL3RNNnY4UXJBc3RDWGU3SUJMVFhKYnJ1OTM4NzZLb0FWVzBRTDdZTy9abzg1MXJuWTNBaW0wNElrMDd2NUtheFNvTlI4cWc4NlhVa3VoaXBHOTJJRDgybGQ2WXp1NVkxODBFVTliVzYyQ2Q5bEo4bVNrbjhDaVBmbDNNNE1BPT0iLCJtYWMiOiJmNzIxZjQzOWQ0ZDJiOGUxNTU3ODZlZmI3YzllYmI3MGRhNjQ3YWIxZTc2NGUyNTMzOTg4NjA1YmIyZGRiYzAwIiwidGFnIjoiIn0%3D; csrf_cookie_name=31b9054b05b0d9b5545a2fe1a4456521; ci_session=103c0193e898db6337056350f5e4d385"}, "cookies": {"admin_auth": "eyJpdiI6IkQ3T3RUQngyc043Q3RZSWVnSjhWaFE9PSIsInZhbHVlIjoiRTlGaDVKSTMxdm15ODBTM2tvZFdQRldxL3RNNnY4UXJBc3RDWGU3SUJMVFhKYnJ1OTM4NzZLb0FWVzBRTDdZTy9abzg1MXJuWTNBaW0wNElrMDd2NUtheFNvTlI4cWc4NlhVa3VoaXBHOTJJRDgybGQ2WXp1NVkxODBFVTliVzYyQ2Q5bEo4bVNrbjhDaVBmbDNNNE1BPT0iLCJtYWMiOiJmNzIxZjQzOWQ0ZDJiOGUxNTU3ODZlZmI3YzllYmI3MGRhNjQ3YWIxZTc2NGUyNTMzOTg4NjA1YmIyZGRiYzAwIiwidGFnIjoiIn0=", "csrf_cookie_name": "31b9054b05b0d9b5545a2fe1a4456521", "ci_session": "103c0193e898db6337056350f5e4d385"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8000/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}