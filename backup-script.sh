#!/bin/bash

# AomProuddyogiki CMS Backup Script
# Created: June 17, 2025
# Purpose: Backup complete CMS implementation

echo "🚀 AomProuddyogiki CMS Backup Script"
echo "======================================"

# Set variables
PROJECT_DIR="/opt/lampp/htdocs/aomF/aomprouddyogiki-website"
BACKUP_DIR="/opt/lampp/htdocs/aomF/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="aomprouddyogiki_cms_backup_${TIMESTAMP}"

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"

echo "📁 Creating backup directory: ${BACKUP_DIR}/${BACKUP_NAME}"
mkdir -p "${BACKUP_DIR}/${BACKUP_NAME}"

# Backup project files
echo "📂 Backing up project files..."
cp -r "${PROJECT_DIR}" "${BACKUP_DIR}/${BACKUP_NAME}/project_files"

# Backup database
echo "🗄️ Backing up database..."
mysqldump -u root aomprouddyogiki_db > "${BACKUP_DIR}/${BACKUP_NAME}/database_backup.sql"

# Create backup info file
echo "📝 Creating backup information file..."
cat > "${BACKUP_DIR}/${BACKUP_NAME}/backup_info.txt" << EOF
AomProuddyogiki CMS Backup Information
=====================================

Backup Date: $(date)
Backup Location: ${BACKUP_DIR}/${BACKUP_NAME}
Project Directory: ${PROJECT_DIR}
Database: aomprouddyogiki_db

Files Included:
- Complete project directory
- Database dump (aomprouddyogiki_db)
- All CMS implementation files
- Admin panel and authentication system
- All views, controllers, and models

CMS Features Backed Up:
✅ Admin authentication system
✅ Page management with rich text editor
✅ Menu management system
✅ Content blocks system
✅ Professional admin interface
✅ Database schema with 4 CMS tables
✅ Security features and validation

Admin Access:
- URL: http://localhost:8000/admin/login
- Username: admin
- Password: admin123

Restore Instructions:
1. Copy project files to web server directory
2. Import database_backup.sql to MySQL
3. Configure web server (Apache/Nginx)
4. Update environment variables if needed
5. Access admin panel with provided credentials

Implementation Status: ✅ COMPLETED
Ready for: Production deployment, further development
EOF

# Create restore script
echo "🔧 Creating restore script..."
cat > "${BACKUP_DIR}/${BACKUP_NAME}/restore.sh" << 'EOF'
#!/bin/bash

# AomProuddyogiki CMS Restore Script
echo "🔄 Restoring AomProuddyogiki CMS..."

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run as root or with sudo"
    exit 1
fi

# Set variables
RESTORE_DIR="/opt/lampp/htdocs/aomF"
BACKUP_DIR=$(dirname "$0")

echo "📁 Restoring project files..."
cp -r "${BACKUP_DIR}/project_files" "${RESTORE_DIR}/aomprouddyogiki-website"

echo "🗄️ Restoring database..."
mysql -u root -e "CREATE DATABASE IF NOT EXISTS aomprouddyogiki_db;"
mysql -u root aomprouddyogiki_db < "${BACKUP_DIR}/database_backup.sql"

echo "🔧 Setting permissions..."
chown -R www-data:www-data "${RESTORE_DIR}/aomprouddyogiki-website"
chmod -R 755 "${RESTORE_DIR}/aomprouddyogiki-website"

echo "✅ Restore completed!"
echo "🌐 Access website: http://localhost:8000/"
echo "🔐 Access admin: http://localhost:8000/admin/login (admin/admin123)"
echo ""
echo "To start development server:"
echo "cd ${RESTORE_DIR}/aomprouddyogiki-website"
echo "php -S localhost:8000 -t public/"
EOF

chmod +x "${BACKUP_DIR}/${BACKUP_NAME}/restore.sh"

# Create compressed archive
echo "📦 Creating compressed archive..."
cd "${BACKUP_DIR}"
tar -czf "${BACKUP_NAME}.tar.gz" "${BACKUP_NAME}"

# Calculate sizes
PROJECT_SIZE=$(du -sh "${PROJECT_DIR}" | cut -f1)
BACKUP_SIZE=$(du -sh "${BACKUP_NAME}" | cut -f1)
ARCHIVE_SIZE=$(du -sh "${BACKUP_NAME}.tar.gz" | cut -f1)

echo ""
echo "✅ Backup completed successfully!"
echo "======================================"
echo "📊 Backup Statistics:"
echo "   Original project size: ${PROJECT_SIZE}"
echo "   Backup directory size: ${BACKUP_SIZE}"
echo "   Compressed archive size: ${ARCHIVE_SIZE}"
echo ""
echo "📁 Backup Location:"
echo "   Directory: ${BACKUP_DIR}/${BACKUP_NAME}/"
echo "   Archive: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
echo ""
echo "🔄 To restore this backup:"
echo "   1. Extract: tar -xzf ${BACKUP_NAME}.tar.gz"
echo "   2. Run: sudo ./${BACKUP_NAME}/restore.sh"
echo ""
echo "📋 Backup Contents:"
echo "   ✅ Complete project files"
echo "   ✅ Database dump"
echo "   ✅ CMS implementation"
echo "   ✅ Admin panel"
echo "   ✅ All configurations"
echo "   ✅ Restore script"
echo ""
echo "🎯 Implementation Status: COMPLETED"
echo "🚀 Ready for production deployment!"
EOF
