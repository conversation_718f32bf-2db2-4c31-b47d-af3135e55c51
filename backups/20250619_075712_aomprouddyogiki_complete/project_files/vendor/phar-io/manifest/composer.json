{"name": "phar-io/manifest", "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "support": {"issues": "https://github.com/phar-io/manifest/issues"}, "require": {"php": "^7.2 || ^8.0", "ext-dom": "*", "ext-phar": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1"}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}}