#!/bin/bash

# AomProuddyogiki Website Restoration Script
# Usage: ./restore.sh [target_directory] [database_name] [db_user] [db_password]

echo "🚀 AomProuddyogiki Website Restoration Script"
echo "=============================================="

# Default values
TARGET_DIR=${1:-"/var/www/html/aomprouddyogiki-website"}
DB_NAME=${2:-"aomprouddyogiki_db"}
DB_USER=${3:-"root"}
DB_PASS=${4:-""}

echo "📋 Configuration:"
echo "Target Directory: $TARGET_DIR"
echo "Database Name: $DB_NAME"
echo "Database User: $DB_USER"
echo ""

# Check if running as root for permissions
if [[ $EUID -eq 0 ]]; then
   echo "⚠️  Running as root - will set proper permissions"
   SET_PERMISSIONS=true
else
   echo "ℹ️  Not running as root - you may need to set permissions manually"
   SET_PERMISSIONS=false
fi

echo ""
read -p "Continue with restoration? (y/N): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Restoration cancelled"
    exit 1
fi

echo ""
echo "🗄️  Step 1: Creating database..."

# Create database
mysql -u $DB_USER -p$DB_PASS -e "CREATE DATABASE IF NOT EXISTS $DB_NAME;" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Database '$DB_NAME' created/verified"
else
    echo "❌ Failed to create database. Please check credentials."
    exit 1
fi

# Import database backup
echo "📥 Importing database backup..."
mysql -u $DB_USER -p$DB_PASS $DB_NAME < database_backup.sql
if [ $? -eq 0 ]; then
    echo "✅ Database backup imported successfully"
else
    echo "❌ Failed to import database backup"
    exit 1
fi

echo ""
echo "📁 Step 2: Copying project files..."

# Create target directory
mkdir -p $TARGET_DIR
if [ $? -eq 0 ]; then
    echo "✅ Target directory created: $TARGET_DIR"
else
    echo "❌ Failed to create target directory"
    exit 1
fi

# Copy project files
cp -r project_files/* $TARGET_DIR/
if [ $? -eq 0 ]; then
    echo "✅ Project files copied successfully"
else
    echo "❌ Failed to copy project files"
    exit 1
fi

echo ""
echo "🔧 Step 3: Setting up configuration..."

# Create .env file if it doesn't exist
if [ ! -f "$TARGET_DIR/.env" ]; then
    cat > "$TARGET_DIR/.env" << EOF
#--------------------------------------------------------------------
# Environment Configuration
#--------------------------------------------------------------------

CI_ENVIRONMENT = production

#--------------------------------------------------------------------
# App Configuration
#--------------------------------------------------------------------

app.baseURL = 'http://localhost/'
app.indexPage = ''

#--------------------------------------------------------------------
# Database Configuration
#--------------------------------------------------------------------

database.default.hostname = localhost
database.default.database = $DB_NAME
database.default.username = $DB_USER
database.default.password = $DB_PASS
database.default.DBDriver = MySQLi
database.default.DBPrefix = 

#--------------------------------------------------------------------
# Security Configuration
#--------------------------------------------------------------------

encryption.key = $(openssl rand -base64 32)
EOF
    echo "✅ Environment configuration created"
else
    echo "ℹ️  Environment file already exists - please update manually if needed"
fi

echo ""
echo "🔐 Step 4: Setting permissions..."

if [ "$SET_PERMISSIONS" = true ]; then
    # Set directory permissions
    chmod -R 755 $TARGET_DIR
    chmod -R 777 $TARGET_DIR/writable
    chmod -R 777 $TARGET_DIR/public/uploads
    
    # Set ownership (assuming www-data user)
    if id "www-data" &>/dev/null; then
        chown -R www-data:www-data $TARGET_DIR
        echo "✅ Permissions and ownership set (www-data)"
    else
        echo "⚠️  www-data user not found - please set ownership manually"
    fi
else
    echo "⚠️  Please set permissions manually:"
    echo "   chmod -R 755 $TARGET_DIR"
    echo "   chmod -R 777 $TARGET_DIR/writable"
    echo "   chmod -R 777 $TARGET_DIR/public/uploads"
    echo "   chown -R www-data:www-data $TARGET_DIR"
fi

echo ""
echo "🧪 Step 5: Testing installation..."

# Test database connection
cd $TARGET_DIR
if [ -f "spark" ]; then
    php spark migrate:status > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Database connection successful"
    else
        echo "⚠️  Database connection test failed - please check configuration"
    fi
else
    echo "⚠️  CodeIgniter spark file not found - please verify installation"
fi

echo ""
echo "🎉 Restoration Complete!"
echo "======================="
echo ""
echo "📋 Next Steps:"
echo "1. Update base URL in $TARGET_DIR/app/Config/App.php"
echo "2. Configure web server to point to $TARGET_DIR/public/"
echo "3. Test admin access: http://your-domain.com/admin"
echo "4. Default credentials: admin / admin123"
echo ""
echo "🔧 Key URLs to test:"
echo "   - Homepage: http://your-domain.com/"
echo "   - Admin Panel: http://your-domain.com/admin"
echo "   - Services: http://your-domain.com/services"
echo "   - Page Management: http://your-domain.com/admin/pages"
echo ""
echo "📞 Support:"
echo "   - Check logs in: $TARGET_DIR/writable/logs/"
echo "   - Verify permissions on writable/ and public/uploads/"
echo "   - Update database credentials in .env if needed"
echo ""
echo "✅ AomProuddyogiki Website restoration completed successfully!"
